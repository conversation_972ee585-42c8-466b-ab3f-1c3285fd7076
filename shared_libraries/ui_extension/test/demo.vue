<script lang="ts">
import { defineComponent } from 'vue';

export default defineComponent({
  name: 'demo.vue',
  data() {
    return {
      message: 'Hello UI Extension!'
    }
  }
});
</script>

<template>
  <div class="demo-container">
    <h1>{{ message }}</h1>
    <p>这是一个 UI Extension 的演示页面</p>
    <button @click="message = 'Button clicked!'">点击我</button>
  </div>
</template>

<style scoped>
.demo-container {
  padding: 20px;
  text-align: center;
  font-family: Arial, sans-serif;
}

h1 {
  color: #2c3e50;
  margin-bottom: 20px;
}

p {
  color: #7f8c8d;
  margin-bottom: 20px;
}

button {
  background-color: #3498db;
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 5px;
  cursor: pointer;
  font-size: 16px;
}

button:hover {
  background-color: #2980b9;
}
</style>
