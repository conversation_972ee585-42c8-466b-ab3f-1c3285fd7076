{"name": "@moxo/ui-ext", "version": "0.0.1", "main": "lib/cjs/index.cjs", "module": "lib/es/index.js", "types": "lib/types/index.d.ts", "author": "colin", "license": "MIT", "private": true, "type": "module", "files": ["lib"], "exports": {".": {"types": "./lib/types/index.d.ts", "import": "./lib/es/index.js", "require": "./lib/cjs/index.cjs"}, "./thumbnail": {"types": "./lib/types/thumbnail/index.d.ts", "import": "./lib/es/thumbnail/index.js", "require": "./lib/cjs/thumbnail/index.cjs"}}, "scripts": {"precommit": "lint-staged", "build": "vite build --mode es", "dev": "vite --root ./test --mode development", "preview": "vite preview", "lint": "eslint --ext .ts,.vue src", "lint:report": "pnpm lint --format json --output-file report.json", "prepack": "pnpm build", "test": "vitest src", "test:ci": "pnpm run test --silent --coverage"}, "devDependencies": {"@moxo/tsconfig": "workspace:*", "@vitejs/plugin-vue": "^5.2.4", "typescript": "^5.8.3", "vite": "6.0.0", "vite-plugin-dts": "^4.5.4", "vue-tsc": "^3.0.1"}, "dependencies": {"uuid": "^11.1.0"}, "peerDependencies": {"vue": "^3.5.17"}}