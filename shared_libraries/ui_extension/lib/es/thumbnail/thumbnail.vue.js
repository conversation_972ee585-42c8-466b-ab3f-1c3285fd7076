import e from "./thumbnail.vue2.js";
import { createElementBlock as o, openBlock as r, createElementVNode as n } from "vue";
import i from "../_virtual/_plugin-vue_export-helper.js";
const l = { class: "ui-ext-thumbnail" };
function s(m, t, c, p, a, u) {
  return r(), o("div", l, t[0] || (t[0] = [
    n("div", null, null, -1)
  ]));
}
const x = /* @__PURE__ */ i(e, [["render", s]]);
export {
  x as default
};
